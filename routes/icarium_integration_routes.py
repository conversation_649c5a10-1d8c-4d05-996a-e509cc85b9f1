"""
Icarium Integration Routes
Smart automation features for Icarium-Talaria integration
"""

import logging
from datetime import datetime, timedelta
from flask import Blueprint, current_app, jsonify, request
from flask_login import login_required

from core.auth.decorators import admin_required
from database.db_config import get_db_cursor

# Create blueprint
icarium_bp = Blueprint('icarium', __name__, url_prefix='/api/icarium')

# Configure logging
logger = logging.getLogger(__name__)


@icarium_bp.route('/check-wafer-exists', methods=['POST'])
@login_required
def check_wafer_exists():
    """
    Check if a wafer exists in Icarium database before manual addition.
    Prevents duplicate entries and suggests sync instead.
    """
    try:
        data = request.get_json()
        wafer_id = data.get('wafer_id', '').strip()
        
        if not wafer_id:
            return jsonify({
                'success': False,
                'message': 'Wafer ID is required'
            }), 400
        
        logger.info(f"🔍 Checking if wafer {wafer_id} exists in Icarium")
        
        # Check Icarium database
        icarium_result = check_wafer_in_icarium(wafer_id)
        
        # Check Talaria inventory
        talaria_result = check_wafer_in_talaria(wafer_id)
        
        response = {
            'success': True,
            'wafer_id': wafer_id,
            'exists_in_icarium': icarium_result['exists'],
            'exists_in_talaria': talaria_result['exists'],
            'icarium_data': icarium_result.get('data'),
            'talaria_data': talaria_result.get('data'),
            'recommendation': get_recommendation(icarium_result, talaria_result)
        }
        
        logger.info(f"✅ Wafer check completed: Icarium={icarium_result['exists']}, Talaria={talaria_result['exists']}")
        return jsonify(response)
        
    except Exception as e:
        logger.error(f"❌ Error checking wafer existence: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'Error checking wafer: {str(e)}'
        }), 500


def check_wafer_in_icarium(wafer_id):
    """Check if wafer exists in Icarium database"""
    try:
        with get_db_cursor() as cursor:
            # Query Icarium wafer table
            cursor.execute("""
                SELECT 
                    wafer_id,
                    lot_id,
                    location_id,
                    status,
                    created_at,
                    updated_at
                FROM wafers 
                WHERE wafer_id = %s
                LIMIT 1
            """, (wafer_id,))
            
            result = cursor.fetchone()
            
            if result:
                return {
                    'exists': True,
                    'data': {
                        'wafer_id': result['wafer_id'],
                        'lot_id': result['lot_id'],
                        'location_id': result['location_id'],
                        'status': result['status'],
                        'created_at': result['created_at'].isoformat() if result['created_at'] else None,
                        'updated_at': result['updated_at'].isoformat() if result['updated_at'] else None
                    }
                }
            else:
                return {'exists': False}
                
    except Exception as e:
        logger.error(f"❌ Error checking Icarium: {str(e)}")
        return {'exists': False, 'error': str(e)}


def check_wafer_in_talaria(wafer_id):
    """Check if wafer exists in Talaria inventory"""
    try:
        with get_db_cursor() as cursor:
            # Query Talaria wafer inventory table
            cursor.execute("""
                SELECT 
                    wafer_id,
                    lot,
                    location,
                    status,
                    arrival_date
                FROM wafer_inventory 
                WHERE wafer_id = %s
                LIMIT 1
            """, (wafer_id,))
            
            result = cursor.fetchone()
            
            if result:
                return {
                    'exists': True,
                    'data': {
                        'wafer_id': result['wafer_id'],
                        'lot': result['lot'],
                        'location': result['location'],
                        'status': result['status'],
                        'arrival_date': result['arrival_date'].isoformat() if result['arrival_date'] else None
                    }
                }
            else:
                return {'exists': False}
                
    except Exception as e:
        logger.error(f"❌ Error checking Talaria: {str(e)}")
        return {'exists': False, 'error': str(e)}


def get_recommendation(icarium_result, talaria_result):
    """Generate smart recommendation based on wafer existence"""
    icarium_exists = icarium_result['exists']
    talaria_exists = talaria_result['exists']
    
    if icarium_exists and talaria_exists:
        return {
            'action': 'already_synced',
            'message': 'Wafer already exists in both Icarium and Talaria',
            'suggestion': 'No action needed - wafer is already synchronized',
            'priority': 'info'
        }
    elif icarium_exists and not talaria_exists:
        return {
            'action': 'sync_recommended',
            'message': 'Wafer exists in Icarium but not in Talaria',
            'suggestion': 'Sync from Icarium instead of manual entry',
            'priority': 'warning'
        }
    elif not icarium_exists and talaria_exists:
        return {
            'action': 'conflict',
            'message': 'Wafer exists in Talaria but not in Icarium',
            'suggestion': 'Check data consistency - this should not happen',
            'priority': 'error'
        }
    else:
        return {
            'action': 'safe_to_add',
            'message': 'Wafer does not exist in either system',
            'suggestion': 'Safe to add manually or check wafer ID',
            'priority': 'success'
        }


@icarium_bp.route('/sync-wafer', methods=['POST'])
@login_required
def sync_wafer_from_icarium():
    """
    Sync a specific wafer from Icarium to Talaria inventory
    """
    try:
        data = request.get_json()
        wafer_id = data.get('wafer_id', '').strip()
        
        if not wafer_id:
            return jsonify({
                'success': False,
                'message': 'Wafer ID is required'
            }), 400
        
        logger.info(f"🔄 Syncing wafer {wafer_id} from Icarium to Talaria")
        
        # Get wafer data from Icarium
        icarium_result = check_wafer_in_icarium(wafer_id)
        
        if not icarium_result['exists']:
            return jsonify({
                'success': False,
                'message': 'Wafer not found in Icarium'
            }), 404
        
        # Check if already exists in Talaria
        talaria_result = check_wafer_in_talaria(wafer_id)
        
        if talaria_result['exists']:
            return jsonify({
                'success': False,
                'message': 'Wafer already exists in Talaria inventory'
            }), 409
        
        # Sync wafer to Talaria
        sync_result = sync_wafer_to_talaria(icarium_result['data'])
        
        if sync_result['success']:
            logger.info(f"✅ Wafer {wafer_id} synced successfully")
            return jsonify({
                'success': True,
                'message': f'Wafer {wafer_id} synced successfully from Icarium',
                'data': sync_result['data']
            })
        else:
            return jsonify({
                'success': False,
                'message': f'Failed to sync wafer: {sync_result["error"]}'
            }), 500
            
    except Exception as e:
        logger.error(f"❌ Error syncing wafer: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'Error syncing wafer: {str(e)}'
        }), 500


def sync_wafer_to_talaria(icarium_data):
    """Sync wafer data from Icarium to Talaria inventory"""
    try:
        with get_db_cursor() as cursor:
            # Insert wafer into Talaria inventory
            cursor.execute("""
                INSERT INTO wafer_inventory (
                    wafer_id, 
                    lot, 
                    location, 
                    status, 
                    arrival_date,
                    sync_source,
                    sync_timestamp
                ) VALUES (%s, %s, %s, %s, %s, %s, %s)
            """, (
                icarium_data['wafer_id'],
                icarium_data['lot_id'],
                icarium_data['location_id'] or 'Ligentec FR',  # Default location
                icarium_data['status'] or 'Available',
                datetime.now(),  # Set arrival date to now
                'icarium_sync',
                datetime.now()
            ))
            
            return {
                'success': True,
                'data': {
                    'wafer_id': icarium_data['wafer_id'],
                    'lot': icarium_data['lot_id'],
                    'location': icarium_data['location_id'] or 'Ligentec FR',
                    'status': icarium_data['status'] or 'Available'
                }
            }
            
    except Exception as e:
        logger.error(f"❌ Error syncing to Talaria: {str(e)}")
        return {'success': False, 'error': str(e)}
