"""
Icarium Integration Routes
Smart automation features for Icarium-Talaria integration
"""

import logging
from datetime import datetime, timedelta

from flask import Blueprint, current_app, jsonify, request

from core.auth.auth import admin_required, login_required
from core.services.icarium_monitor_service import IcariumMonitorService
from core.services.scheduler_service import get_scheduler_service
from core.services.smart_sync_engine import SmartSyncEngine
from database.db_config import get_db_cursor

# Create blueprint
icarium_bp = Blueprint("icarium", __name__, url_prefix="/api/icarium")

# Configure logging
logger = logging.getLogger(__name__)


@icarium_bp.route("/check-wafer-exists", methods=["POST"])
@login_required
def check_wafer_exists():
    """
    Check if a wafer exists in Icarium database before manual addition.
    Prevents duplicate entries and suggests sync instead.
    """
    try:
        data = request.get_json()
        wafer_id = data.get("wafer_id", "").strip()

        if not wafer_id:
            return jsonify({"success": False, "message": "Wafer ID is required"}), 400

        logger.info(f"🔍 Checking if wafer {wafer_id} exists in Icarium")

        # Check Icarium database
        icarium_result = check_wafer_in_icarium(wafer_id)

        # Check Talaria inventory
        talaria_result = check_wafer_in_talaria(wafer_id)

        response = {
            "success": True,
            "wafer_id": wafer_id,
            "exists_in_icarium": icarium_result["exists"],
            "exists_in_talaria": talaria_result["exists"],
            "icarium_data": icarium_result.get("data"),
            "talaria_data": talaria_result.get("data"),
            "recommendation": get_recommendation(icarium_result, talaria_result),
        }

        logger.info(
            f"✅ Wafer check completed: Icarium={icarium_result['exists']}, Talaria={talaria_result['exists']}"
        )
        return jsonify(response)

    except Exception as e:
        logger.error(f"❌ Error checking wafer existence: {str(e)}")
        return (
            jsonify({"success": False, "message": f"Error checking wafer: {str(e)}"}),
            500,
        )


def check_wafer_in_icarium(wafer_id):
    """Check if wafer exists in Icarium database"""
    try:
        with get_db_cursor() as cursor:
            # Query Icarium wafer table
            cursor.execute(
                """
                SELECT 
                    wafer_id,
                    lot_id,
                    location_id,
                    status,
                    created_at,
                    updated_at
                FROM wafers 
                WHERE wafer_id = %s
                LIMIT 1
            """,
                (wafer_id,),
            )

            result = cursor.fetchone()

            if result:
                return {
                    "exists": True,
                    "data": {
                        "wafer_id": result["wafer_id"],
                        "lot_id": result["lot_id"],
                        "location_id": result["location_id"],
                        "status": result["status"],
                        "created_at": (
                            result["created_at"].isoformat()
                            if result["created_at"]
                            else None
                        ),
                        "updated_at": (
                            result["updated_at"].isoformat()
                            if result["updated_at"]
                            else None
                        ),
                    },
                }
            else:
                return {"exists": False}

    except Exception as e:
        logger.error(f"❌ Error checking Icarium: {str(e)}")
        return {"exists": False, "error": str(e)}


def check_wafer_in_talaria(wafer_id):
    """Check if wafer exists in Talaria inventory"""
    try:
        with get_db_cursor() as cursor:
            # Query Talaria wafer inventory table
            cursor.execute(
                """
                SELECT 
                    wafer_id,
                    lot,
                    location,
                    status,
                    arrival_date
                FROM wafer_inventory 
                WHERE wafer_id = %s
                LIMIT 1
            """,
                (wafer_id,),
            )

            result = cursor.fetchone()

            if result:
                return {
                    "exists": True,
                    "data": {
                        "wafer_id": result["wafer_id"],
                        "lot": result["lot"],
                        "location": result["location"],
                        "status": result["status"],
                        "arrival_date": (
                            result["arrival_date"].isoformat()
                            if result["arrival_date"]
                            else None
                        ),
                    },
                }
            else:
                return {"exists": False}

    except Exception as e:
        logger.error(f"❌ Error checking Talaria: {str(e)}")
        return {"exists": False, "error": str(e)}


def get_recommendation(icarium_result, talaria_result):
    """Generate smart recommendation based on wafer existence"""
    icarium_exists = icarium_result["exists"]
    talaria_exists = talaria_result["exists"]

    if icarium_exists and talaria_exists:
        return {
            "action": "already_synced",
            "message": "Wafer already exists in both Icarium and Talaria",
            "suggestion": "No action needed - wafer is already synchronized",
            "priority": "info",
        }
    elif icarium_exists and not talaria_exists:
        return {
            "action": "sync_recommended",
            "message": "Wafer exists in Icarium but not in Talaria",
            "suggestion": "Sync from Icarium instead of manual entry",
            "priority": "warning",
        }
    elif not icarium_exists and talaria_exists:
        return {
            "action": "conflict",
            "message": "Wafer exists in Talaria but not in Icarium",
            "suggestion": "Check data consistency - this should not happen",
            "priority": "error",
        }
    else:
        return {
            "action": "safe_to_add",
            "message": "Wafer does not exist in either system",
            "suggestion": "Safe to add manually or check wafer ID",
            "priority": "success",
        }


@icarium_bp.route("/sync-wafer", methods=["POST"])
@login_required
def sync_wafer_from_icarium():
    """
    Sync a specific wafer from Icarium to Talaria inventory
    """
    try:
        data = request.get_json()
        wafer_id = data.get("wafer_id", "").strip()

        if not wafer_id:
            return jsonify({"success": False, "message": "Wafer ID is required"}), 400

        logger.info(f"🔄 Syncing wafer {wafer_id} from Icarium to Talaria")

        # Get wafer data from Icarium
        icarium_result = check_wafer_in_icarium(wafer_id)

        if not icarium_result["exists"]:
            return (
                jsonify({"success": False, "message": "Wafer not found in Icarium"}),
                404,
            )

        # Check if already exists in Talaria
        talaria_result = check_wafer_in_talaria(wafer_id)

        if talaria_result["exists"]:
            return (
                jsonify(
                    {
                        "success": False,
                        "message": "Wafer already exists in Talaria inventory",
                    }
                ),
                409,
            )

        # Sync wafer to Talaria
        sync_result = sync_wafer_to_talaria(icarium_result["data"])

        if sync_result["success"]:
            logger.info(f"✅ Wafer {wafer_id} synced successfully")
            return jsonify(
                {
                    "success": True,
                    "message": f"Wafer {wafer_id} synced successfully from Icarium",
                    "data": sync_result["data"],
                }
            )
        else:
            return (
                jsonify(
                    {
                        "success": False,
                        "message": f'Failed to sync wafer: {sync_result["error"]}',
                    }
                ),
                500,
            )

    except Exception as e:
        logger.error(f"❌ Error syncing wafer: {str(e)}")
        return (
            jsonify({"success": False, "message": f"Error syncing wafer: {str(e)}"}),
            500,
        )


def sync_wafer_to_talaria(icarium_data):
    """Sync wafer data from Icarium to Talaria inventory"""
    try:
        with get_db_cursor() as cursor:
            # Insert wafer into Talaria inventory
            cursor.execute(
                """
                INSERT INTO wafer_inventory (
                    wafer_id, 
                    lot, 
                    location, 
                    status, 
                    arrival_date,
                    sync_source,
                    sync_timestamp
                ) VALUES (%s, %s, %s, %s, %s, %s, %s)
            """,
                (
                    icarium_data["wafer_id"],
                    icarium_data["lot_id"],
                    icarium_data["location_id"] or "Ligentec FR",  # Default location
                    icarium_data["status"] or "Available",
                    datetime.now(),  # Set arrival date to now
                    "icarium_sync",
                    datetime.now(),
                ),
            )

            return {
                "success": True,
                "data": {
                    "wafer_id": icarium_data["wafer_id"],
                    "lot": icarium_data["lot_id"],
                    "location": icarium_data["location_id"] or "Ligentec FR",
                    "status": icarium_data["status"] or "Available",
                },
            }

    except Exception as e:
        logger.error(f"❌ Error syncing to Talaria: {str(e)}")
        return {"success": False, "error": str(e)}


@icarium_bp.route("/check-new-wafers", methods=["GET"])
@login_required
def check_new_wafers():
    """
    Check for new wafers in Icarium that need to be synced to Talaria
    """
    try:
        hours_back = request.args.get("hours", 24, type=int)

        logger.info(f"🔍 Checking for new wafers (last {hours_back} hours)")

        monitor_service = IcariumMonitorService()
        result = monitor_service.check_new_wafers(hours_back)

        if result["success"]:
            return jsonify(result)
        else:
            return jsonify(result), 500

    except Exception as e:
        logger.error(f"❌ Error checking new wafers: {str(e)}")
        return (
            jsonify(
                {"success": False, "message": f"Error checking new wafers: {str(e)}"}
            ),
            500,
        )


@icarium_bp.route("/send-daily-notification", methods=["POST"])
@login_required
@admin_required
def send_daily_notification():
    """
    Manually trigger daily notification about new wafers
    """
    try:
        data = request.get_json() or {}
        hours_back = data.get("hours_back", 24)

        logger.info(
            f"📧 Manually triggering daily notification (last {hours_back} hours)"
        )

        monitor_service = IcariumMonitorService()
        result = monitor_service.send_daily_notification(hours_back)

        return jsonify(result)

    except Exception as e:
        logger.error(f"❌ Error sending daily notification: {str(e)}")
        return (
            jsonify(
                {"success": False, "message": f"Error sending notification: {str(e)}"}
            ),
            500,
        )


@icarium_bp.route("/sync-recommendations", methods=["GET"])
@login_required
def get_sync_recommendations():
    """
    Get smart recommendations for syncing wafers
    """
    try:
        logger.info("🧠 Generating sync recommendations")

        monitor_service = IcariumMonitorService()
        result = monitor_service.get_sync_recommendations()

        return jsonify(result)

    except Exception as e:
        logger.error(f"❌ Error getting sync recommendations: {str(e)}")
        return (
            jsonify(
                {
                    "success": False,
                    "message": f"Error getting recommendations: {str(e)}",
                }
            ),
            500,
        )


@icarium_bp.route("/scheduler/status", methods=["GET"])
@login_required
@admin_required
def get_scheduler_status():
    """
    Get status of scheduled tasks
    """
    try:
        scheduler = get_scheduler_service()
        tasks = scheduler.get_task_status()

        return jsonify(
            {
                "success": True,
                "running": scheduler.running,
                "tasks": tasks,
                "total_tasks": len(tasks),
            }
        )

    except Exception as e:
        logger.error(f"❌ Error getting scheduler status: {str(e)}")
        return (
            jsonify(
                {
                    "success": False,
                    "message": f"Error getting scheduler status: {str(e)}",
                }
            ),
            500,
        )


@icarium_bp.route("/scheduler/start", methods=["POST"])
@login_required
@admin_required
def start_scheduler():
    """
    Start the scheduler service
    """
    try:
        scheduler = get_scheduler_service()
        scheduler.start()

        return jsonify({"success": True, "message": "Scheduler started successfully"})

    except Exception as e:
        logger.error(f"❌ Error starting scheduler: {str(e)}")
        return (
            jsonify(
                {"success": False, "message": f"Error starting scheduler: {str(e)}"}
            ),
            500,
        )


@icarium_bp.route("/scheduler/stop", methods=["POST"])
@login_required
@admin_required
def stop_scheduler():
    """
    Stop the scheduler service
    """
    try:
        scheduler = get_scheduler_service()
        scheduler.stop()

        return jsonify({"success": True, "message": "Scheduler stopped successfully"})

    except Exception as e:
        logger.error(f"❌ Error stopping scheduler: {str(e)}")
        return (
            jsonify(
                {"success": False, "message": f"Error stopping scheduler: {str(e)}"}
            ),
            500,
        )


@icarium_bp.route("/scheduler/run-task", methods=["POST"])
@login_required
@admin_required
def run_task_manually():
    """
    Manually run a scheduled task
    """
    try:
        data = request.get_json()
        task_name = data.get("task_name")

        if not task_name:
            return jsonify({"success": False, "message": "Task name is required"}), 400

        scheduler = get_scheduler_service()
        result = scheduler.run_task_now(task_name)

        return jsonify(result)

    except Exception as e:
        logger.error(f"❌ Error running task manually: {str(e)}")
        return (
            jsonify({"success": False, "message": f"Error running task: {str(e)}"}),
            500,
        )


@icarium_bp.route("/smart-sync-recommendations", methods=["GET"])
@login_required
def get_smart_sync_recommendations():
    """
    Get intelligent sync recommendations from the smart sync engine
    """
    try:
        hours_back = request.args.get("hours", 168, type=int)  # Default: 1 week

        logger.info(
            f"🧠 Generating smart sync recommendations (last {hours_back} hours)"
        )

        smart_engine = SmartSyncEngine()
        result = smart_engine.generate_smart_recommendations(hours_back)

        return jsonify(result)

    except Exception as e:
        logger.error(f"❌ Error getting smart sync recommendations: {str(e)}")
        return (
            jsonify(
                {
                    "success": False,
                    "message": f"Error getting smart recommendations: {str(e)}",
                }
            ),
            500,
        )


@icarium_bp.route("/execute-smart-recommendation", methods=["POST"])
@login_required
def execute_smart_recommendation():
    """
    Execute a smart sync recommendation
    """
    try:
        data = request.get_json()
        recommendation_id = data.get("recommendation_id")
        action_type = data.get("action_type")
        parameters = data.get("parameters", {})

        if not recommendation_id or not action_type:
            return (
                jsonify(
                    {
                        "success": False,
                        "message": "Recommendation ID and action type are required",
                    }
                ),
                400,
            )

        logger.info(
            f"🚀 Executing smart recommendation: {recommendation_id} ({action_type})"
        )

        # Execute the recommendation based on action type
        result = execute_sync_action(action_type, parameters)

        return jsonify(result)

    except Exception as e:
        logger.error(f"❌ Error executing smart recommendation: {str(e)}")
        return (
            jsonify(
                {
                    "success": False,
                    "message": f"Error executing recommendation: {str(e)}",
                }
            ),
            500,
        )


def execute_sync_action(action_type: str, parameters: Dict) -> Dict:
    """Execute a specific sync action"""
    try:
        if action_type == "batch_sync_lot":
            return batch_sync_by_lot(parameters.get("lot_id"))

        elif action_type == "sync_by_location":
            return sync_by_location(parameters.get("location"))

        elif action_type == "sync_urgent_wafers":
            return sync_urgent_wafers(parameters.get("max_age_hours", 48))

        elif action_type == "sync_by_status":
            return sync_by_status(parameters.get("status"))

        else:
            return {"success": False, "message": f"Unknown action type: {action_type}"}

    except Exception as e:
        logger.error(f"❌ Error in sync action {action_type}: {str(e)}")
        return {"success": False, "message": f"Sync action failed: {str(e)}"}


def batch_sync_by_lot(lot_id: str) -> Dict:
    """Sync all wafers from a specific lot"""
    try:
        # Get wafers from the lot that need syncing
        with get_db_cursor() as cursor:
            cursor.execute(
                """
                SELECT i.wafer_id, i.lot_id, i.location_id, i.status, i.created_at
                FROM wafers i
                LEFT JOIN wafer_inventory t ON i.wafer_id = t.wafer_id
                WHERE i.lot_id = %s AND t.wafer_id IS NULL
            """,
                (lot_id,),
            )

            wafers_to_sync = cursor.fetchall()

        if not wafers_to_sync:
            return {"success": False, "message": f"No wafers to sync for lot {lot_id}"}

        # Sync each wafer
        success_count = 0
        error_count = 0
        results = []

        for wafer_row in wafers_to_sync:
            try:
                wafer_data = {
                    "wafer_id": wafer_row["wafer_id"],
                    "lot_id": wafer_row["lot_id"],
                    "location_id": wafer_row["location_id"],
                    "status": wafer_row["status"],
                }

                sync_result = sync_wafer_to_talaria(wafer_data)

                if sync_result["success"]:
                    success_count += 1
                    results.append({"wafer_id": wafer_row["wafer_id"], "success": True})
                else:
                    error_count += 1
                    results.append(
                        {
                            "wafer_id": wafer_row["wafer_id"],
                            "success": False,
                            "error": sync_result["error"],
                        }
                    )

            except Exception as e:
                error_count += 1
                results.append(
                    {
                        "wafer_id": wafer_row["wafer_id"],
                        "success": False,
                        "error": str(e),
                    }
                )

        logger.info(
            f"✅ Batch sync lot {lot_id}: {success_count} success, {error_count} errors"
        )

        return {
            "success": True,
            "message": f"Batch sync completed for lot {lot_id}",
            "results": {
                "total": len(wafers_to_sync),
                "success": success_count,
                "errors": error_count,
                "success_rate": round(success_count / len(wafers_to_sync) * 100, 1),
                "details": results,
            },
        }

    except Exception as e:
        logger.error(f"❌ Error in batch sync by lot: {str(e)}")
        return {"success": False, "message": f"Batch sync failed: {str(e)}"}


def sync_by_location(location: str) -> Dict:
    """Sync all wafers from a specific location"""
    try:
        # Get wafers from the location that need syncing
        with get_db_cursor() as cursor:
            cursor.execute(
                """
                SELECT i.wafer_id, i.lot_id, i.location_id, i.status, i.created_at
                FROM wafers i
                LEFT JOIN wafer_inventory t ON i.wafer_id = t.wafer_id
                WHERE i.location_id = %s AND t.wafer_id IS NULL
            """,
                (location,),
            )

            wafers_to_sync = cursor.fetchall()

        if not wafers_to_sync:
            return {
                "success": False,
                "message": f"No wafers to sync for location {location}",
            }

        # Sync wafers (similar logic to batch_sync_by_lot)
        success_count = 0
        error_count = 0

        for wafer_row in wafers_to_sync:
            try:
                wafer_data = {
                    "wafer_id": wafer_row["wafer_id"],
                    "lot_id": wafer_row["lot_id"],
                    "location_id": wafer_row["location_id"],
                    "status": wafer_row["status"],
                }

                sync_result = sync_wafer_to_talaria(wafer_data)

                if sync_result["success"]:
                    success_count += 1
                else:
                    error_count += 1

            except Exception:
                error_count += 1

        logger.info(
            f"✅ Location sync {location}: {success_count} success, {error_count} errors"
        )

        return {
            "success": True,
            "message": f"Location sync completed for {location}",
            "results": {
                "total": len(wafers_to_sync),
                "success": success_count,
                "errors": error_count,
                "success_rate": round(success_count / len(wafers_to_sync) * 100, 1),
            },
        }

    except Exception as e:
        logger.error(f"❌ Error in sync by location: {str(e)}")
        return {"success": False, "message": f"Location sync failed: {str(e)}"}


def sync_urgent_wafers(max_age_hours: int) -> Dict:
    """Sync wafers older than specified age"""
    try:
        cutoff_time = datetime.now() - timedelta(hours=max_age_hours)

        # Get urgent wafers
        with get_db_cursor() as cursor:
            cursor.execute(
                """
                SELECT i.wafer_id, i.lot_id, i.location_id, i.status, i.created_at
                FROM wafers i
                LEFT JOIN wafer_inventory t ON i.wafer_id = t.wafer_id
                WHERE i.created_at <= %s AND t.wafer_id IS NULL
                ORDER BY i.created_at ASC
            """,
                (cutoff_time,),
            )

            urgent_wafers = cursor.fetchall()

        if not urgent_wafers:
            return {
                "success": True,
                "message": f"No urgent wafers found (older than {max_age_hours} hours)",
                "results": {"total": 0, "success": 0, "errors": 0},
            }

        # Sync urgent wafers
        success_count = 0
        error_count = 0

        for wafer_row in urgent_wafers:
            try:
                wafer_data = {
                    "wafer_id": wafer_row["wafer_id"],
                    "lot_id": wafer_row["lot_id"],
                    "location_id": wafer_row["location_id"],
                    "status": wafer_row["status"],
                }

                sync_result = sync_wafer_to_talaria(wafer_data)

                if sync_result["success"]:
                    success_count += 1
                else:
                    error_count += 1

            except Exception:
                error_count += 1

        logger.info(f"✅ Urgent sync: {success_count} success, {error_count} errors")

        return {
            "success": True,
            "message": f"Urgent sync completed for wafers older than {max_age_hours} hours",
            "results": {
                "total": len(urgent_wafers),
                "success": success_count,
                "errors": error_count,
                "success_rate": round(success_count / len(urgent_wafers) * 100, 1),
            },
        }

    except Exception as e:
        logger.error(f"❌ Error in urgent sync: {str(e)}")
        return {"success": False, "message": f"Urgent sync failed: {str(e)}"}
