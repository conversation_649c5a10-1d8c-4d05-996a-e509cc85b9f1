/* Global theme variables */
:root {
  --bg-primary-light: #ffffff;
  --bg-secondary-light: #f8f9fa;
  --text-primary-light: #212529;
  --text-secondary-light: #495057;
  --border-light: #dee2e6;
  --input-bg-light: #f8f9fa;
  --input-border-light: #ced4da;
  --btn-primary-light: #007bff;
  --btn-secondary-light: #6c757d;
  --btn-danger-light: #dc3545;
  --table-border-light: #dee2e6;
  --modal-bg-light: #ffffff;
  
  /* Dark theme */
  --bg-primary-dark: #1a1a1a;
  --bg-secondary-dark: #2d2d2d;
  --text-primary-dark: #f8f9fa;
  --text-secondary-dark: #adb5bd;
  --border-dark: #495057;
  --input-bg-dark: #343a40;
  --input-border-dark: #495057;
  --btn-primary-dark: #0d6efd;
  --btn-secondary-dark: #5c636a;
  --btn-danger-dark: #dc3545;
  --table-border-dark: #495057;
  --modal-bg-dark: #343a40;
}

/* Form groups */
.form-group {
  margin-bottom: 20px;
}

/* Form inputs */
input, select, textarea {
  width: 100%;
  padding: 10px;
  border-radius: 6px;
  border: 1px solid var(--input-border-light);
  font-size: 16px;
  background-color: var(--input-bg-light);
  color: var(--text-primary-light);
  transition: background-color 0.3s, color 0.3s, border-color 0.3s;
}

textarea[readonly], input[readonly], select[disabled] {
  background-color: #e9ecef;
  cursor: not-allowed;
}

.form-label {
  font-size: 1.1rem;
  color: var(--text-primary-light);
  transition: color 0.3s;
}

/* Buttons */
.btn {
  padding: 10px 20px;
  margin-top: 15px;
  transition: background-color 0.3s, border-color 0.3s;
}

.btn-primary {
  background-color: var(--btn-primary-light);
  border-color: var(--btn-primary-light);
  color: white;
}

.btn-secondary {
  background-color: var(--btn-secondary-light);
  border-color: var(--btn-secondary-light);
  color: white;
}

.btn-danger {
  background-color: var(--btn-danger-light);
  border-color: var(--btn-danger-light);
  color: white;
}

/* Tables */
.table {
  color: var(--text-primary-light);
  transition: color 0.3s;
}

.table-bordered {
  border: 1px solid var(--table-border-light);
}

.table-bordered th,
.table-bordered td {
  padding: 15px;
  text-align: left;
  vertical-align: middle;
  border-color: var(--table-border-light);
}

.table-responsive {
  margin-top: 20px;
}

/* Modals */
.modal-title {
  font-weight: bold;
  color: var(--text-primary-light);
}

.modal-content {
  padding: 20px;
  background-color: var(--modal-bg-light);
  transition: background-color 0.3s;
}

/* Preview */
#previewIframe {
  border-radius: 5px;
  border: 1px solid var(--border-light);
}

.iframe-style {
  height: 500px;
}

/* Logo */
.logo {
  height: auto;
  max-height: 80px;
  width: auto;
  max-width: 150px;
}

/* Dark mode styles */
.dark input, .dark select, .dark textarea {
  background-color: var(--input-bg-dark);
  border-color: var(--input-border-dark);
  color: var(--text-primary-dark);
}

.dark .form-label {
  color: var(--text-primary-dark);
}

.dark .btn-primary {
  background-color: var(--btn-primary-dark);
  border-color: var(--btn-primary-dark);
}

.dark .btn-secondary {
  background-color: var(--btn-secondary-dark);
  border-color: var(--btn-secondary-dark);
}

.dark .btn-danger {
  background-color: var(--btn-danger-dark);
  border-color: var(--btn-danger-dark);
}

.dark .table {
  color: var(--text-primary-dark);
}

.dark .table-bordered {
  border-color: var(--table-border-dark);
}

.dark .table-bordered th,
.dark .table-bordered td {
  border-color: var(--table-border-dark);
}

.dark .modal-title {
  color: var(--text-primary-dark);
}

.dark .modal-content {
  background-color: var(--modal-bg-dark);
}

.dark #previewIframe {
  border-color: var(--border-dark);
}

/* Transition all elements */
.dark-transition * {
  transition: background-color 0.3s, color 0.3s, border-color 0.3s;
}

/* Smart Wafer Validation Styles */
.validation-container {
  margin-top: 8px;
  padding: 10px;
  border-radius: 6px;
  font-size: 14px;
  border-left: 4px solid;
  background-color: #f8f9fa;
}

.validation-container.success {
  border-left-color: #28a745;
  background-color: #d4edda;
  color: #155724;
}

.validation-container.warning {
  border-left-color: #ffc107;
  background-color: #fff3cd;
  color: #856404;
}

.validation-container.error {
  border-left-color: #dc3545;
  background-color: #f8d7da;
  color: #721c24;
}

.validation-container.info {
  border-left-color: #17a2b8;
  background-color: #d1ecf1;
  color: #0c5460;
}

.validation-container.loading {
  border-left-color: #6c757d;
  background-color: #e9ecef;
  color: #495057;
}

.validation-message {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

.validation-icon {
  margin-right: 8px;
  font-size: 16px;
}

.validation-text {
  flex: 1;
}

.btn-sync-wafer,
.btn-details {
  background: rgba(255, 255, 255, 0.8);
  border: 1px solid rgba(0, 0, 0, 0.1);
  border-radius: 4px;
  padding: 4px 8px;
  margin: 2px 4px 2px 0;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.btn-sync-wafer:hover,
.btn-details:hover {
  background: rgba(255, 255, 255, 1);
  border-color: rgba(0, 0, 0, 0.2);
  transform: translateY(-1px);
}

.btn-sync-wafer {
  color: #007bff;
}

.btn-details {
  color: #6c757d;
}

.btn-sync-wafer:hover {
  color: #0056b3;
}

.btn-details:hover {
  color: #495057;
}

/* Icarium Sync Widget Styles */
.icarium-sync-widget {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  margin-bottom: 20px;
}

.widget-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.widget-title {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
}

.widget-title i {
  margin-right: 8px;
}

.widget-actions {
  display: flex;
  gap: 8px;
}

.btn-refresh,
.btn-settings {
  background: rgba(255, 255, 255, 0.2);
  border: none;
  border-radius: 6px;
  color: white;
  padding: 6px 8px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.btn-refresh:hover,
.btn-settings:hover {
  background: rgba(255, 255, 255, 0.3);
}

.widget-content {
  padding: 20px;
}

.loading-state,
.error-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  color: #6c757d;
}

.loading-state i,
.error-state i {
  font-size: 24px;
  margin-bottom: 12px;
}

.error-state {
  color: #dc3545;
}

.btn-retry {
  margin-top: 12px;
  padding: 6px 12px;
  background: #007bff;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

.sync-status {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.status-header {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 600;
}

.sync-status.success .status-icon { color: #28a745; }
.sync-status.warning .status-icon { color: #ffc107; }
.sync-status.error .status-icon { color: #dc3545; }
.sync-status.info .status-icon { color: #17a2b8; }

.sync-metrics {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 16px;
}

.metric {
  text-align: center;
  padding: 12px;
  background: #f8f9fa;
  border-radius: 8px;
}

.metric-value {
  font-size: 24px;
  font-weight: bold;
  color: #495057;
}

.metric-label {
  font-size: 12px;
  color: #6c757d;
  margin-top: 4px;
}

.lots-summary {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 12px;
}

.lots-header {
  display: flex;
  align-items: center;
  gap: 6px;
  font-weight: 600;
  margin-bottom: 8px;
  color: #495057;
}

.lots-list {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.lot-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 4px 8px;
  background: white;
  border-radius: 4px;
  font-size: 14px;
}

.lot-id {
  font-weight: 500;
}

.lot-count {
  color: #6c757d;
  font-size: 12px;
}

.action-buttons {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.btn-action {
  flex: 1;
  min-width: 120px;
  padding: 10px 16px;
  border: none;
  border-radius: 6px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
}

.btn-action:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.btn-primary {
  background: #007bff;
  color: white;
}

.btn-primary:hover:not(:disabled) {
  background: #0056b3;
}

.btn-secondary {
  background: #6c757d;
  color: white;
}

.btn-secondary:hover:not(:disabled) {
  background: #545b62;
}

.btn-success {
  background: #28a745;
  color: white;
}

.widget-footer {
  padding: 8px 20px;
  background: #f8f9fa;
  border-top: 1px solid #e9ecef;
  text-align: center;
}

.last-updated {
  color: #6c757d;
  font-size: 12px;
}

/* Batch Sync Dialog Styles */
.batch-sync-dialog {
  text-align: left;
}

.batch-recommendations {
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin-top: 16px;
}

.batch-rec-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px;
  background: #f8f9fa;
  border-radius: 8px;
  border-left: 4px solid #007bff;
}

.rec-info {
  flex: 1;
}

.rec-info strong {
  display: block;
  margin-bottom: 4px;
}

.rec-info p {
  margin: 0;
  font-size: 14px;
  color: #6c757d;
}

.btn-sync-lot {
  background: #007bff;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 6px 12px;
  cursor: pointer;
  font-size: 12px;
  white-space: nowrap;
}

.btn-sync-lot:hover {
  background: #0056b3;
}

/* Sync Settings Styles */
.sync-settings {
  text-align: left;
}

.setting-item {
  margin-bottom: 16px;
}

.setting-item label {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 500;
}

.setting-item select {
  margin-top: 4px;
  padding: 4px 8px;
  border: 1px solid #ddd;
  border-radius: 4px;
  width: 100%;
}
