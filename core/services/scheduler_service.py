"""
Scheduler Service
Handles scheduled tasks like daily notifications
"""

import logging
import threading
import time
from datetime import datetime, timedelta
from typing import Dict, List

from core.services.icarium_monitor_service import IcariumMonitorService

# Configure logging
logger = logging.getLogger(__name__)


class SchedulerService:
    """Service for managing scheduled tasks"""

    def __init__(self):
        self.running = False
        self.thread = None
        self.tasks = []
        self.icarium_monitor = IcariumMonitorService()
        self.setup_default_tasks()

    def setup_default_tasks(self):
        """Setup default scheduled tasks"""
        # Daily new wafer notification at 9:00 AM
        self.add_task(
            name="daily_new_wafer_notification",
            schedule_type="daily",
            schedule_time="09:00",
            function=self.run_daily_wafer_notification,
            description="Check for new wafers and send daily notification"
        )
        
        # Hourly new wafer check (for real-time monitoring)
        self.add_task(
            name="hourly_wafer_check",
            schedule_type="hourly",
            schedule_time="00",  # At the top of each hour
            function=self.run_hourly_wafer_check,
            description="Check for new wafers every hour"
        )
        
        # Weekly sync recommendations on Monday at 8:00 AM
        self.add_task(
            name="weekly_sync_recommendations",
            schedule_type="weekly",
            schedule_time="monday:08:00",
            function=self.run_weekly_sync_recommendations,
            description="Generate weekly sync recommendations"
        )

    def add_task(self, name: str, schedule_type: str, schedule_time: str, function, description: str = ""):
        """Add a scheduled task"""
        task = {
            'name': name,
            'schedule_type': schedule_type,  # daily, hourly, weekly
            'schedule_time': schedule_time,
            'function': function,
            'description': description,
            'last_run': None,
            'next_run': self.calculate_next_run(schedule_type, schedule_time),
            'enabled': True,
            'run_count': 0,
            'error_count': 0
        }
        
        self.tasks.append(task)
        logger.info(f"📅 Added scheduled task: {name} ({schedule_type} at {schedule_time})")

    def calculate_next_run(self, schedule_type: str, schedule_time: str) -> datetime:
        """Calculate next run time for a task"""
        now = datetime.now()
        
        if schedule_type == "hourly":
            # schedule_time is minutes (e.g., "00" for top of hour)
            minutes = int(schedule_time)
            next_run = now.replace(minute=minutes, second=0, microsecond=0)
            if next_run <= now:
                next_run += timedelta(hours=1)
                
        elif schedule_type == "daily":
            # schedule_time is "HH:MM" (e.g., "09:00")
            hour, minute = map(int, schedule_time.split(':'))
            next_run = now.replace(hour=hour, minute=minute, second=0, microsecond=0)
            if next_run <= now:
                next_run += timedelta(days=1)
                
        elif schedule_type == "weekly":
            # schedule_time is "day:HH:MM" (e.g., "monday:08:00")
            day_name, time_str = schedule_time.split(':')
            hour, minute = int(time_str.split(':')[0]), int(time_str.split(':')[1])
            
            # Map day names to weekday numbers (Monday=0)
            days = {'monday': 0, 'tuesday': 1, 'wednesday': 2, 'thursday': 3, 
                   'friday': 4, 'saturday': 5, 'sunday': 6}
            target_weekday = days[day_name.lower()]
            
            # Calculate days until target weekday
            days_ahead = target_weekday - now.weekday()
            if days_ahead <= 0:  # Target day already happened this week
                days_ahead += 7
                
            next_run = now + timedelta(days=days_ahead)
            next_run = next_run.replace(hour=hour, minute=minute, second=0, microsecond=0)
            
        else:
            # Default to 1 hour from now
            next_run = now + timedelta(hours=1)
            
        return next_run

    def start(self):
        """Start the scheduler"""
        if self.running:
            logger.warning("⚠️ Scheduler already running")
            return
            
        self.running = True
        self.thread = threading.Thread(target=self._run_scheduler, daemon=True)
        self.thread.start()
        logger.info("🚀 Scheduler started")

    def stop(self):
        """Stop the scheduler"""
        self.running = False
        if self.thread:
            self.thread.join(timeout=5)
        logger.info("⏹️ Scheduler stopped")

    def _run_scheduler(self):
        """Main scheduler loop"""
        logger.info("🔄 Scheduler loop started")
        
        while self.running:
            try:
                now = datetime.now()
                
                # Check each task
                for task in self.tasks:
                    if not task['enabled']:
                        continue
                        
                    if now >= task['next_run']:
                        self._run_task(task)
                        
                # Sleep for 60 seconds before next check
                time.sleep(60)
                
            except Exception as e:
                logger.error(f"❌ Scheduler error: {str(e)}")
                time.sleep(60)  # Continue after error

    def _run_task(self, task: Dict):
        """Run a scheduled task"""
        try:
            logger.info(f"🏃 Running scheduled task: {task['name']}")
            
            # Update task status
            task['last_run'] = datetime.now()
            task['run_count'] += 1
            
            # Run the task function
            result = task['function']()
            
            # Calculate next run time
            task['next_run'] = self.calculate_next_run(
                task['schedule_type'], 
                task['schedule_time']
            )
            
            logger.info(f"✅ Task {task['name']} completed. Next run: {task['next_run']}")
            
            return result
            
        except Exception as e:
            logger.error(f"❌ Task {task['name']} failed: {str(e)}")
            task['error_count'] += 1
            
            # Calculate next run time even on error
            task['next_run'] = self.calculate_next_run(
                task['schedule_type'], 
                task['schedule_time']
            )

    def run_daily_wafer_notification(self) -> Dict:
        """Run daily new wafer notification"""
        try:
            logger.info("📧 Running daily new wafer notification")
            
            result = self.icarium_monitor.send_daily_notification(hours_back=24)
            
            if result['success'] and result['notification_sent']:
                logger.info(f"✅ Daily notification sent: {result['message']}")
            else:
                logger.info(f"ℹ️ Daily notification: {result['message']}")
                
            return result
            
        except Exception as e:
            logger.error(f"❌ Daily notification failed: {str(e)}")
            return {'success': False, 'error': str(e)}

    def run_hourly_wafer_check(self) -> Dict:
        """Run hourly wafer check for real-time monitoring"""
        try:
            logger.info("🔍 Running hourly wafer check")
            
            # Check for new wafers in the last hour
            result = self.icarium_monitor.check_new_wafers(hours_back=1)
            
            if result['success'] and result['summary']['needs_attention']:
                # Send urgent notification for immediate attention
                urgent_result = self.icarium_monitor.send_daily_notification(hours_back=1)
                logger.info(f"🚨 Urgent notification sent for {result['summary']['total_unsynced_wafers']} new wafers")
                return urgent_result
            else:
                logger.info("✅ Hourly check: No new wafers requiring immediate attention")
                return result
                
        except Exception as e:
            logger.error(f"❌ Hourly wafer check failed: {str(e)}")
            return {'success': False, 'error': str(e)}

    def run_weekly_sync_recommendations(self) -> Dict:
        """Run weekly sync recommendations"""
        try:
            logger.info("📊 Running weekly sync recommendations")
            
            result = self.icarium_monitor.get_sync_recommendations()
            
            if result['success'] and result['recommendations']:
                # Create notification with recommendations
                from core.services.notification_service import NotificationService
                notification_service = NotificationService()
                
                recommendations_text = "\n".join([
                    f"• {rec['title']}: {rec['description']}"
                    for rec in result['recommendations'][:5]  # Top 5 recommendations
                ])
                
                notification_service.create_notification(
                    title=f"📊 Weekly Sync Recommendations",
                    message=f"Found {len(result['recommendations'])} sync recommendations for this week.",
                    notification_type='info',
                    category='weekly_recommendations',
                    priority='medium',
                    data={
                        'recommendations': result['recommendations'],
                        'total_unsynced': result['total_unsynced'],
                        'lots_affected': result['lots_affected']
                    },
                    expires_at=datetime.now() + timedelta(days=7),
                    action_url='/inventory'
                )
                
                logger.info(f"✅ Weekly recommendations generated: {len(result['recommendations'])} items")
                
            return result
            
        except Exception as e:
            logger.error(f"❌ Weekly recommendations failed: {str(e)}")
            return {'success': False, 'error': str(e)}

    def get_task_status(self) -> List[Dict]:
        """Get status of all scheduled tasks"""
        return [
            {
                'name': task['name'],
                'description': task['description'],
                'schedule_type': task['schedule_type'],
                'schedule_time': task['schedule_time'],
                'enabled': task['enabled'],
                'last_run': task['last_run'].isoformat() if task['last_run'] else None,
                'next_run': task['next_run'].isoformat() if task['next_run'] else None,
                'run_count': task['run_count'],
                'error_count': task['error_count']
            }
            for task in self.tasks
        ]

    def enable_task(self, task_name: str) -> Dict:
        """Enable a scheduled task"""
        for task in self.tasks:
            if task['name'] == task_name:
                task['enabled'] = True
                logger.info(f"✅ Enabled task: {task_name}")
                return {'success': True, 'message': f'Task {task_name} enabled'}
        
        return {'success': False, 'message': f'Task {task_name} not found'}

    def disable_task(self, task_name: str) -> Dict:
        """Disable a scheduled task"""
        for task in self.tasks:
            if task['name'] == task_name:
                task['enabled'] = False
                logger.info(f"⏸️ Disabled task: {task_name}")
                return {'success': True, 'message': f'Task {task_name} disabled'}
        
        return {'success': False, 'message': f'Task {task_name} not found'}

    def run_task_now(self, task_name: str) -> Dict:
        """Manually run a task immediately"""
        for task in self.tasks:
            if task['name'] == task_name:
                logger.info(f"🏃 Manually running task: {task_name}")
                return self._run_task(task)
        
        return {'success': False, 'message': f'Task {task_name} not found'}


# Global scheduler instance
scheduler_service = None

def get_scheduler_service():
    """Get global scheduler service instance"""
    global scheduler_service
    if scheduler_service is None:
        scheduler_service = SchedulerService()
    return scheduler_service
