"""
Icarium Monitor Service
Monitors Icarium database for new wafers and sends notifications
"""

import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional

from database.db_config import get_db_cursor
from core.services.notification_service import NotificationService

# Configure logging
logger = logging.getLogger(__name__)


class IcariumMonitorService:
    """Service for monitoring Icarium database and detecting new wafers"""

    def __init__(self):
        self.notification_service = NotificationService()

    def check_new_wafers(self, hours_back: int = 24) -> Dict:
        """
        Check for new wafers added to Icarium in the last N hours
        
        Args:
            hours_back: Number of hours to look back for new wafers
            
        Returns:
            Dictionary with new wafers and summary information
        """
        try:
            logger.info(f"🔍 Checking for new wafers in last {hours_back} hours")
            
            # Get cutoff time
            cutoff_time = datetime.now() - timedelta(hours=hours_back)
            
            # Find new wafers in Icarium
            new_wafers = self._get_new_wafers_from_icarium(cutoff_time)
            
            # Check which ones are not yet in Talaria
            unsynced_wafers = self._filter_unsynced_wafers(new_wafers)
            
            # Get summary statistics
            summary = self._generate_summary(new_wafers, unsynced_wafers, hours_back)
            
            logger.info(f"✅ Found {len(new_wafers)} new wafers, {len(unsynced_wafers)} unsynced")
            
            return {
                'success': True,
                'new_wafers': new_wafers,
                'unsynced_wafers': unsynced_wafers,
                'summary': summary,
                'check_time': datetime.now().isoformat(),
                'hours_back': hours_back
            }
            
        except Exception as e:
            logger.error(f"❌ Error checking new wafers: {str(e)}")
            return {
                'success': False,
                'error': str(e),
                'new_wafers': [],
                'unsynced_wafers': [],
                'summary': {}
            }

    def _get_new_wafers_from_icarium(self, cutoff_time: datetime) -> List[Dict]:
        """Get wafers added to Icarium after cutoff time"""
        try:
            with get_db_cursor() as cursor:
                cursor.execute("""
                    SELECT 
                        wafer_id,
                        lot_id,
                        location_id,
                        status,
                        created_at,
                        updated_at
                    FROM wafers 
                    WHERE created_at >= %s
                    ORDER BY created_at DESC
                    LIMIT 100
                """, (cutoff_time,))
                
                results = cursor.fetchall()
                
                return [
                    {
                        'wafer_id': row['wafer_id'],
                        'lot_id': row['lot_id'],
                        'location_id': row['location_id'],
                        'status': row['status'],
                        'created_at': row['created_at'].isoformat() if row['created_at'] else None,
                        'updated_at': row['updated_at'].isoformat() if row['updated_at'] else None
                    }
                    for row in results
                ]
                
        except Exception as e:
            logger.error(f"❌ Error querying Icarium: {str(e)}")
            return []

    def _filter_unsynced_wafers(self, new_wafers: List[Dict]) -> List[Dict]:
        """Filter wafers that are not yet in Talaria inventory"""
        if not new_wafers:
            return []
            
        try:
            wafer_ids = [w['wafer_id'] for w in new_wafers]
            
            with get_db_cursor() as cursor:
                # Get wafers that already exist in Talaria
                placeholders = ','.join(['%s'] * len(wafer_ids))
                cursor.execute(f"""
                    SELECT DISTINCT wafer_id 
                    FROM wafer_inventory 
                    WHERE wafer_id IN ({placeholders})
                """, wafer_ids)
                
                existing_wafer_ids = {row['wafer_id'] for row in cursor.fetchall()}
                
                # Return wafers that don't exist in Talaria
                unsynced = [
                    wafer for wafer in new_wafers 
                    if wafer['wafer_id'] not in existing_wafer_ids
                ]
                
                return unsynced
                
        except Exception as e:
            logger.error(f"❌ Error filtering unsynced wafers: {str(e)}")
            return new_wafers  # Return all if we can't check

    def _generate_summary(self, new_wafers: List[Dict], unsynced_wafers: List[Dict], hours_back: int) -> Dict:
        """Generate summary statistics"""
        # Group by lot
        lots = {}
        for wafer in new_wafers:
            lot_id = wafer['lot_id']
            if lot_id not in lots:
                lots[lot_id] = {'total': 0, 'unsynced': 0}
            lots[lot_id]['total'] += 1
            
        for wafer in unsynced_wafers:
            lot_id = wafer['lot_id']
            if lot_id in lots:
                lots[lot_id]['unsynced'] += 1

        # Group by location
        locations = {}
        for wafer in unsynced_wafers:
            location = wafer['location_id'] or 'Unknown'
            locations[location] = locations.get(location, 0) + 1

        return {
            'total_new_wafers': len(new_wafers),
            'total_unsynced_wafers': len(unsynced_wafers),
            'sync_percentage': round((len(new_wafers) - len(unsynced_wafers)) / len(new_wafers) * 100, 1) if new_wafers else 100,
            'lots_affected': len(lots),
            'lots_summary': lots,
            'locations_summary': locations,
            'time_period': f"Last {hours_back} hours",
            'needs_attention': len(unsynced_wafers) > 0
        }

    def send_daily_notification(self, hours_back: int = 24) -> Dict:
        """
        Send daily notification about new wafers
        
        Args:
            hours_back: Hours to look back for new wafers
            
        Returns:
            Result of notification sending
        """
        try:
            logger.info("📧 Preparing daily new wafer notification")
            
            # Check for new wafers
            check_result = self.check_new_wafers(hours_back)
            
            if not check_result['success']:
                return {
                    'success': False,
                    'message': 'Failed to check for new wafers',
                    'error': check_result.get('error')
                }
            
            summary = check_result['summary']
            unsynced_wafers = check_result['unsynced_wafers']
            
            # Only send notification if there are unsynced wafers
            if not summary['needs_attention']:
                logger.info("✅ No unsynced wafers found, skipping notification")
                return {
                    'success': True,
                    'message': 'No new wafers to sync',
                    'notification_sent': False
                }
            
            # Generate notification content
            notification_data = self._generate_notification_content(summary, unsynced_wafers)
            
            # Send dashboard notification
            dashboard_result = self._send_dashboard_notification(notification_data)
            
            # Send email notification
            email_result = self._send_email_notification(notification_data)
            
            return {
                'success': True,
                'message': f'Notification sent for {len(unsynced_wafers)} unsynced wafers',
                'notification_sent': True,
                'dashboard_notification': dashboard_result,
                'email_notification': email_result,
                'summary': summary
            }
            
        except Exception as e:
            logger.error(f"❌ Error sending daily notification: {str(e)}")
            return {
                'success': False,
                'message': f'Failed to send notification: {str(e)}',
                'notification_sent': False
            }

    def _generate_notification_content(self, summary: Dict, unsynced_wafers: List[Dict]) -> Dict:
        """Generate notification content"""
        # Create title
        count = summary['total_unsynced_wafers']
        title = f"🔔 {count} New Wafer{'s' if count != 1 else ''} Ready to Sync"
        
        # Create message
        lots_text = f"{summary['lots_affected']} lot{'s' if summary['lots_affected'] != 1 else ''}"
        message = f"Found {count} new wafers in {lots_text} that need to be synced from Icarium to Talaria."
        
        # Create detailed summary
        details = []
        details.append(f"📊 **Summary ({summary['time_period']}):**")
        details.append(f"• Total new wafers in Icarium: {summary['total_new_wafers']}")
        details.append(f"• Wafers needing sync: {summary['total_unsynced_wafers']}")
        details.append(f"• Current sync rate: {summary['sync_percentage']}%")
        
        if summary['lots_summary']:
            details.append(f"\n📦 **Lots affected:**")
            for lot_id, counts in summary['lots_summary'].items():
                if counts['unsynced'] > 0:
                    details.append(f"• {lot_id}: {counts['unsynced']} unsynced of {counts['total']} total")
        
        if summary['locations_summary']:
            details.append(f"\n📍 **Locations:**")
            for location, count in summary['locations_summary'].items():
                details.append(f"• {location}: {count} wafers")
        
        # Create action items
        actions = [
            "🔄 Go to Inventory → Sync from Icarium",
            "📋 Review wafer details before syncing",
            "✅ Verify locations and lot assignments"
        ]
        
        return {
            'title': title,
            'message': message,
            'details': '\n'.join(details),
            'actions': actions,
            'wafer_count': count,
            'lots_count': summary['lots_affected'],
            'unsynced_wafers': unsynced_wafers[:10]  # Limit to first 10 for display
        }

    def _send_dashboard_notification(self, notification_data: Dict) -> Dict:
        """Send notification to dashboard"""
        try:
            # Create dashboard notification
            notification = {
                'title': notification_data['title'],
                'message': notification_data['message'],
                'type': 'warning' if notification_data['wafer_count'] > 10 else 'info',
                'category': 'icarium_sync',
                'priority': 'high' if notification_data['wafer_count'] > 20 else 'medium',
                'data': {
                    'wafer_count': notification_data['wafer_count'],
                    'lots_count': notification_data['lots_count'],
                    'action_url': '/inventory',
                    'details': notification_data['details']
                },
                'expires_at': datetime.now() + timedelta(hours=24)
            }
            
            # Send via notification service
            result = self.notification_service.create_notification(
                user_id=None,  # System notification for all users
                **notification
            )
            
            return {'success': True, 'notification_id': result.get('id')}
            
        except Exception as e:
            logger.error(f"❌ Error sending dashboard notification: {str(e)}")
            return {'success': False, 'error': str(e)}

    def _send_email_notification(self, notification_data: Dict) -> Dict:
        """Send email notification to inventory team"""
        try:
            # Email configuration
            recipients = ['<EMAIL>']  # Add more emails as needed
            subject = f"Talaria: {notification_data['wafer_count']} New Wafers Need Syncing"
            
            # Create email body
            body = f"""
{notification_data['message']}

{notification_data['details']}

**Next Steps:**
{chr(10).join(f"• {action}" for action in notification_data['actions'])}

**Quick Actions:**
• Access Talaria Dashboard: [Your Dashboard URL]
• Go directly to Inventory: [Your Inventory URL]

---
This is an automated notification from Talaria Dashboard.
Generated at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
            """.strip()
            
            # Send email (implement based on your email service)
            # For now, just log the email content
            logger.info(f"📧 Email notification prepared for {recipients}")
            logger.info(f"Subject: {subject}")
            logger.debug(f"Body: {body}")
            
            return {
                'success': True,
                'recipients': recipients,
                'subject': subject
            }
            
        except Exception as e:
            logger.error(f"❌ Error sending email notification: {str(e)}")
            return {'success': False, 'error': str(e)}

    def get_sync_recommendations(self) -> Dict:
        """Get smart recommendations for syncing wafers"""
        try:
            # Check for new wafers
            check_result = self.check_new_wafers(hours_back=168)  # Last week
            
            if not check_result['success']:
                return {'success': False, 'recommendations': []}
            
            unsynced_wafers = check_result['unsynced_wafers']
            
            recommendations = []
            
            # Group by lot for batch sync recommendations
            lots = {}
            for wafer in unsynced_wafers:
                lot_id = wafer['lot_id']
                if lot_id not in lots:
                    lots[lot_id] = []
                lots[lot_id].append(wafer)
            
            # Generate recommendations
            for lot_id, wafers in lots.items():
                if len(wafers) >= 5:
                    recommendations.append({
                        'type': 'batch_sync',
                        'priority': 'high',
                        'title': f'Batch Sync Lot {lot_id}',
                        'description': f'Sync {len(wafers)} wafers from lot {lot_id} at once',
                        'wafer_count': len(wafers),
                        'lot_id': lot_id,
                        'action': 'sync_lot'
                    })
                elif len(wafers) >= 1:
                    recommendations.append({
                        'type': 'individual_sync',
                        'priority': 'medium',
                        'title': f'Sync {len(wafers)} wafers from {lot_id}',
                        'description': f'Individual sync for wafers in lot {lot_id}',
                        'wafer_count': len(wafers),
                        'lot_id': lot_id,
                        'action': 'sync_individual'
                    })
            
            return {
                'success': True,
                'recommendations': recommendations,
                'total_unsynced': len(unsynced_wafers),
                'lots_affected': len(lots)
            }
            
        except Exception as e:
            logger.error(f"❌ Error generating sync recommendations: {str(e)}")
            return {'success': False, 'recommendations': [], 'error': str(e)}
